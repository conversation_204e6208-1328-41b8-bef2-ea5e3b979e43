export interface Agent {
  id: string
  name: string
  company: string | null
  role: string | null
  phone: string | null
  email: string | null
  avatar_url: string | null
  status: "active" | "inactive"
  created_at: string
  updated_at: string
}

export interface Call {
  id: string
  client_id: string
  agent_id: string | null
  call_id: string
  contact_full_name: string | null
  contact_phone: string | null
  duration: number
  direction: "inbound" | "outbound"
  status: "scheduled" | "in_progress" | "finished" | "failed"
  sentiment: "positive" | "neutral" | "negative"
  call_summary: string | null
  call_transcript: string | null
  audio_url: string | null
  scheduled_date_time: string | null
  created_at: string
  updated_at: string
  agent?: Agent
  client?: {
    id: string
    company_name: string | null
    user: {
      full_name: string | null
      email: string
    }
  }
}

export interface CallStats {
  scheduled: number
  finished: number
  in_progress: number
  failed: number
  total: number
  sentimentStats: {
    positive: number
    neutral: number
    negative: number
  }
  directionStats: {
    inbound: number
    outbound: number
  }
}
