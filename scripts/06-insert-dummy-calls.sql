-- Insert dummy calls data using existing client and agent relationships
-- This script creates 35+ realistic call records

INSERT INTO public.calls (
  client_id, 
  agent_id, 
  call_id, 
  contact_full_name, 
  contact_phone, 
  duration, 
  direction, 
  status, 
  sentiment, 
  call_summary, 
  call_transcript,
  scheduled_date_time,
  created_at
) VALUES

-- Calls for freelancer client (01da703f-8729-46f1-a894-e27e0d37ee82)
(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '372d8ecb-0d35-4439-91e8-785a3434c123', -- <PERSON>
  'rec001FL2024001',
  '<PERSON>',
  '+31612345678',
  420,
  'inbound',
  'finished',
  'positive',
  'Client inquiry about freelance project scope and timeline. Discussed requirements and provided detailed proposal.',
  'Agent: Good morning, this is <PERSON> from 3rdbase. How can I help you today? Customer: Hi, I need information about your freelance services...',
  NOW() - INTERVAL '2 days',
  NOW() - INTERVAL '2 days'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '80c79837-945a-4022-a9c7-bb8d525067db', -- <PERSON>
  'rec002FL2024002',
  '<PERSON> <PERSON>',
  '+31687654321',
  180,
  'outbound',
  'finished',
  'neutral',
  'Follow-up call regarding project deliverables and next steps.',
  'Agent: Hi Michael, this is Amy calling about your project status...',
  NOW() - INTERVAL '1 day',
  NOW() - INTERVAL '1 day'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '4c22d932-4f4d-4c2b-ab82-01e31aebea63', -- Jane Goodhart
  'rec003FL2024003',
  'Emma Thompson',
  '+31698765432',
  600,
  'inbound',
  'finished',
  'positive',
  'New client onboarding call. Explained services and set up initial project parameters.',
  'Agent: Welcome to our freelance platform, Emma. Let me walk you through our services...',
  NOW() - INTERVAL '3 hours',
  NOW() - INTERVAL '3 hours'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '687208ad-9a39-4561-b350-fd9801dc407a', -- Ellis Dekker
  'rec004FL2024004',
  'David Wilson',
  '+31634567890',
  240,
  'inbound',
  'in_progress',
  'neutral',
  'Technical support call regarding platform usage and features.',
  'Agent: Hi David, I understand you are having some issues with the platform...',
  NOW(),
  NOW()
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '87710fa5-aa16-4ce7-9a4b-51a2c076441a', -- Maryam Al Nuami
  'rec005FL2024005',
  'Lisa Anderson',
  '+31645678901',
  90,
  'outbound',
  'failed',
  'negative',
  'Attempted to reach client for project update but call was unsuccessful.',
  'No answer - left voicemail',
  NOW() + INTERVAL '2 hours',
  NOW()
),

-- Calls for 3rdbase client (25d2abe0-f020-4078-8795-e2edf03f7473)
(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '372d8ecb-0d35-4439-91e8-785a3434c123', -- Jared Dunn
  'rec006TB2024001',
  'Robert Johnson',
  '+31656789012',
  480,
  'inbound',
  'finished',
  'positive',
  'Enterprise client consultation about AI implementation and digital transformation strategy.',
  'Agent: Thank you for calling 3rdbase, this is Jared. How can I assist with your AI needs today?',
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '5 days'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '80c79837-945a-4022-a9c7-bb8d525067db', -- Amy Johnson
  'rec007TB2024002',
  'Jennifer Davis',
  '+31667890123',
  720,
  'inbound',
  'finished',
  'positive',
  'Detailed discussion about custom AI agent development and integration requirements.',
  'Agent: Hi Jennifer, Amy here from 3rdbase operations. Let us discuss your AI agent requirements...',
  NOW() - INTERVAL '4 days',
  NOW() - INTERVAL '4 days'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '4c22d932-4f4d-4c2b-ab82-01e31aebea63', -- Jane Goodhart
  'rec008TB2024003',
  'Mark Thompson',
  '+31678901234',
  360,
  'outbound',
  'finished',
  'neutral',
  'Project status update and milestone review call.',
  'Agent: Hi Mark, this is Jane calling for our scheduled project review...',
  NOW() - INTERVAL '3 days',
  NOW() - INTERVAL '3 days'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '687208ad-9a39-4561-b350-fd9801dc407a', -- Ellis Dekker
  'rec009TB2024004',
  'Amanda White',
  '+31689012345',
  540,
  'inbound',
  'finished',
  'positive',
  'Training session for new AI agent features and capabilities.',
  'Agent: Welcome to the training session, Amanda. Today we will cover the new AI features...',
  NOW() - INTERVAL '2 days',
  NOW() - INTERVAL '2 days'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '87710fa5-aa16-4ce7-9a4b-51a2c076441a', -- Maryam Al Nuami
  'rec010TB2024005',
  'Christopher Brown',
  '+31690123456',
  300,
  'inbound',
  'finished',
  'neutral',
  'Technical support for AI agent configuration and optimization.',
  'Agent: Hi Christopher, Maryam here. I understand you need help with agent configuration...',
  NOW() - INTERVAL '1 day',
  NOW() - INTERVAL '1 day'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '88d0dfbe-5d1d-4fe3-aa2a-b56eb6cece9f', -- John Jarvis
  'rec011TB2024006',
  'Michelle Garcia',
  '+31601234567',
  420,
  'outbound',
  'scheduled',
  'neutral',
  'Scheduled consultation for advanced AI features and enterprise solutions.',
  'Scheduled call - not yet completed',
  NOW() + INTERVAL '1 day',
  NOW()
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  'ab485806-751f-4f51-98ac-a5f9e8f9a882', -- Melanie Brooks
  'rec012TB2024007',
  'Kevin Martinez',
  '+31612345679',
  180,
  'inbound',
  'finished',
  'positive',
  'Quick support call for minor technical issue resolution.',
  'Agent: Hi Kevin, this is Melanie from support. How can I help you today?',
  NOW() - INTERVAL '6 hours',
  NOW() - INTERVAL '6 hours'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  'f690bc5f-b50c-4cf8-a482-59faaf607e15', -- John Gruber
  'rec013TB2024008',
  'Rachel Wilson',
  '+31623456780',
  660,
  'inbound',
  'finished',
  'positive',
  'Comprehensive onboarding call for new enterprise client.',
  'Agent: Welcome to 3rdbase, Rachel. This is John and I will be your dedicated support contact...',
  NOW() - INTERVAL '12 hours',
  NOW() - INTERVAL '12 hours'
),

-- Additional calls with varied scenarios
(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '88d0dfbe-5d1d-4fe3-aa2a-b56eb6cece9f', -- John Jarvis
  'rec014FL2024006',
  'Thomas Lee',
  '+31634567891',
  150,
  'outbound',
  'finished',
  'negative',
  'Client complaint resolution call. Addressed concerns about service quality.',
  'Agent: Hi Thomas, this is John calling about your recent concerns...',
  NOW() - INTERVAL '8 hours',
  NOW() - INTERVAL '8 hours'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  'ab485806-751f-4f51-98ac-a5f9e8f9a882', -- Melanie Brooks
  'rec015FL2024007',
  'Nancy Rodriguez',
  '+31645678902',
  390,
  'inbound',
  'finished',
  'positive',
  'Project completion review and feedback collection.',
  'Agent: Hi Nancy, Melanie here. I wanted to review your completed project...',
  NOW() - INTERVAL '4 hours',
  NOW() - INTERVAL '4 hours'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '372d8ecb-0d35-4439-91e8-785a3434c123', -- Jared Dunn
  'rec016TB2024009',
  'Steven Clark',
  '+31656789013',
  510,
  'inbound',
  'in_progress',
  'positive',
  'Live consultation about AI strategy and implementation roadmap.',
  'Agent: Hi Steven, great to speak with you. Let us dive into your AI strategy...',
  NOW(),
  NOW()
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '80c79837-945a-4022-a9c7-bb8d525067db', -- Amy Johnson
  'rec017TB2024010',
  'Patricia Lewis',
  '+31667890124',
  270,
  'outbound',
  'finished',
  'neutral',
  'Follow-up call for project milestone and next phase planning.',
  'Agent: Hi Patricia, Amy calling for our scheduled follow-up...',
  NOW() - INTERVAL '2 hours',
  NOW() - INTERVAL '2 hours'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  'f690bc5f-b50c-4cf8-a482-59faaf607e15', -- John Gruber
  'rec018FL2024008',
  'Daniel Walker',
  '+31678901235',
  120,
  'inbound',
  'failed',
  'negative',
  'Technical issue escalation - call dropped due to connection problems.',
  'Call dropped - technical difficulties',
  NOW() - INTERVAL '1 hour',
  NOW() - INTERVAL '1 hour'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '4c22d932-4f4d-4c2b-ab82-01e31aebea63', -- Jane Goodhart
  'rec019TB2024011',
  'Barbara Hall',
  '+31689012346',
  450,
  'inbound',
  'finished',
  'positive',
  'Strategic planning session for Q2 AI implementation goals.',
  'Agent: Hi Barbara, Jane here. Ready to discuss your Q2 AI goals?',
  NOW() - INTERVAL '30 minutes',
  NOW() - INTERVAL '30 minutes'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '687208ad-9a39-4561-b350-fd9801dc407a', -- Ellis Dekker
  'rec020FL2024009',
  'Richard Allen',
  '+31690123457',
  330,
  'outbound',
  'scheduled',
  'neutral',
  'Scheduled demo call for new freelance platform features.',
  'Scheduled demo - pending',
  NOW() + INTERVAL '3 hours',
  NOW()
),

-- More calls to reach 30+ total
(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '87710fa5-aa16-4ce7-9a4b-51a2c076441a', -- Maryam Al Nuami
  'rec021TB2024012',
  'Susan Young',
  '+31601234568',
  600,
  'inbound',
  'finished',
  'positive',
  'Comprehensive AI agent training and best practices session.',
  'Agent: Hi Susan, Maryam here. Ready for your AI training session?',
  NOW() - INTERVAL '6 days',
  NOW() - INTERVAL '6 days'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '372d8ecb-0d35-4439-91e8-785a3434c123', -- Jared Dunn
  'rec022FL2024010',
  'Joseph King',
  '+31612345680',
  210,
  'inbound',
  'finished',
  'neutral',
  'Billing inquiry and payment processing assistance.',
  'Agent: Hi Joseph, Jared here. I see you have a billing question...',
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '5 days'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '88d0dfbe-5d1d-4fe3-aa2a-b56eb6cece9f', -- John Jarvis
  'rec023TB2024013',
  'Linda Wright',
  '+31623456781',
  480,
  'outbound',
  'finished',
  'positive',
  'Enterprise contract renewal discussion and feature updates.',
  'Agent: Hi Linda, John calling about your contract renewal...',
  NOW() - INTERVAL '4 days',
  NOW() - INTERVAL '4 days'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  'ab485806-751f-4f51-98ac-a5f9e8f9a882', -- Melanie Brooks
  'rec024FL2024011',
  'Charles Lopez',
  '+31634567892',
  360,
  'inbound',
  'finished',
  'positive',
  'Platform tutorial and feature walkthrough for new user.',
  'Agent: Welcome Charles, this is Melanie. Let me show you around the platform...',
  NOW() - INTERVAL '3 days',
  NOW() - INTERVAL '3 days'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  'f690bc5f-b50c-4cf8-a482-59faaf607e15', -- John Gruber
  'rec025TB2024014',
  'Karen Hill',
  '+31645678903',
  540,
  'inbound',
  'finished',
  'positive',
  'Advanced AI configuration and optimization consultation.',
  'Agent: Hi Karen, John here. Ready to optimize your AI agents?',
  NOW() - INTERVAL '2 days',
  NOW() - INTERVAL '2 days'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '80c79837-945a-4022-a9c7-bb8d525067db', -- Amy Johnson
  'rec026FL2024012',
  'Paul Green',
  '+31656789014',
  180,
  'outbound',
  'finished',
  'neutral',
  'Project status check and timeline adjustment discussion.',
  'Agent: Hi Paul, Amy calling for a quick project update...',
  NOW() - INTERVAL '1 day',
  NOW() - INTERVAL '1 day'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '4c22d932-4f4d-4c2b-ab82-01e31aebea63', -- Jane Goodhart
  'rec027TB2024015',
  'Betty Adams',
  '+31667890125',
  420,
  'inbound',
  'in_progress',
  'positive',
  'Live troubleshooting session for AI agent performance issues.',
  'Agent: Hi Betty, Jane here. Let us work through these performance issues together...',
  NOW(),
  NOW()
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '687208ad-9a39-4561-b350-fd9801dc407a', -- Ellis Dekker
  'rec028FL2024013',
  'Frank Baker',
  '+31678901236',
  300,
  'inbound',
  'finished',
  'neutral',
  'General support and platform navigation assistance.',
  'Agent: Hi Frank, Ellis here. How can I help you navigate the platform today?',
  NOW() - INTERVAL '18 hours',
  NOW() - INTERVAL '18 hours'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '87710fa5-aa16-4ce7-9a4b-51a2c076441a', -- Maryam Al Nuami
  'rec029TB2024016',
  'Helen Nelson',
  '+31689012347',
  660,
  'inbound',
  'finished',
  'positive',
  'Enterprise onboarding and custom AI agent setup.',
  'Agent: Welcome to 3rdbase, Helen. This is Maryam and I will guide you through the setup...',
  NOW() - INTERVAL '15 hours',
  NOW() - INTERVAL '15 hours'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '88d0dfbe-5d1d-4fe3-aa2a-b56eb6cece9f', -- John Jarvis
  'rec030FL2024014',
  'Gary Carter',
  '+31690123458',
  240,
  'outbound',
  'scheduled',
  'neutral',
  'Scheduled follow-up for project completion and feedback.',
  'Scheduled follow-up call',
  NOW() + INTERVAL '4 hours',
  NOW()
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  'ab485806-751f-4f51-98ac-a5f9e8f9a882', -- Melanie Brooks
  'rec031TB2024017',
  'Donna Mitchell',
  '+31601234569',
  390,
  'inbound',
  'finished',
  'positive',
  'Technical support for API integration and webhook setup.',
  'Agent: Hi Donna, Melanie from tech support. Let us get your API integration working...',
  NOW() - INTERVAL '12 hours',
  NOW() - INTERVAL '12 hours'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  'f690bc5f-b50c-4cf8-a482-59faaf607e15', -- John Gruber
  'rec032FL2024015',
  'Carl Perez',
  '+31612345681',
  150,
  'inbound',
  'failed',
  'negative',
  'Connection issues during support call - unable to complete.',
  'Technical difficulties - call failed',
  NOW() - INTERVAL '9 hours',
  NOW() - INTERVAL '9 hours'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '372d8ecb-0d35-4439-91e8-785a3434c123', -- Jared Dunn
  'rec033TB2024018',
  'Ruth Roberts',
  '+31623456782',
  720,
  'inbound',
  'finished',
  'positive',
  'Strategic consultation for enterprise AI transformation roadmap.',
  'Agent: Hi Ruth, Jared here. Excited to discuss your AI transformation journey...',
  NOW() - INTERVAL '6 hours',
  NOW() - INTERVAL '6 hours'
),

(
  '01da703f-8729-46f1-a894-e27e0d37ee82',
  '80c79837-945a-4022-a9c7-bb8d525067db', -- Amy Johnson
  'rec034FL2024016',
  'Eugene Turner',
  '+31634567893',
  270,
  'outbound',
  'finished',
  'neutral',
  'Project milestone review and next phase planning.',
  'Agent: Hi Eugene, Amy calling for our milestone review...',
  NOW() - INTERVAL '3 hours',
  NOW() - INTERVAL '3 hours'
),

(
  '25d2abe0-f020-4078-8795-e2edf03f7473',
  '4c22d932-4f4d-4c2b-ab82-01e31aebea63', -- Jane Goodhart
  'rec035TB2024019',
  'Marie Phillips',
  '+31645678904',
  510,
  'inbound',
  'finished',
  'positive',
  'Advanced feature training and optimization best practices.',
  'Agent: Hi Marie, Jane here. Ready to dive into the advanced features?',
  NOW() - INTERVAL '1 hour',
  NOW() - INTERVAL '1 hour'
);
