-- Insert sample agents
INSERT INTO public.agents (name, company, role, phone, email, avatar_url) VALUES
('<PERSON>', '3rdbase, Stadswerk 072', 'Head of Operations', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200'),
('<PERSON>', 'RCN Vakantiewoningen', 'Hostess', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200'),
('<PERSON>', '<PERSON>', 'Manager Operations', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200'),
('<PERSON>', '3rdbase', 'Business Development', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200'),
('<PERSON>', 'Mini', 'Customer Service', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200'),
('<PERSON>', '<PERSON><PERSON><PERSON><PERSON> de <PERSON>', 'Manager', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200'),
('Maryam Al Nuami', 'Emirates', 'Customer Service Agent', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200'),
('Melanie Brooks', 'Cyso', 'Support Specialist', '+***********', '<EMAIL>', '/placeholder.svg?height=200&width=200');

-- Insert sample calls (you'll need to get actual client IDs first)
-- This is a template - replace client_uuid_1, etc. with real client IDs from your database

-- INSERT INTO public.calls (client_id, agent_id, call_id, contact_full_name, contact_phone, duration, direction, status, sentiment, call_summary, scheduled_date_time) VALUES
-- (client_uuid_1, agent_uuid_1, 'reccOPBSdrMW6MYLN', 'Noah', '+***********', 900, 'inbound', 'finished', 'neutral', 'Customer inquiry about baggage allowance for Emirates flight. Agent provided detailed information about luggage policies and restrictions.', NOW() - INTERVAL '2 days'),
-- (client_uuid_1, agent_uuid_2, 'rec123ABC456DEF', 'Christian Bosman', '+***********', 420, 'inbound', 'finished', 'positive', 'Successful booking confirmation and payment processing.', NOW() - INTERVAL '1 day'),
-- Add more sample calls as needed...
