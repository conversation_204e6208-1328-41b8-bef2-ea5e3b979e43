-- This script creates mock data for testing
-- Run this after you have created some test users through the signup process

-- First, let's create some mock conversations for existing clients
-- You'll need to replace the client_id values with actual client IDs from your database

-- Example mock data (you'll need to update the client_id values)
-- INSERT INTO public.conversations (client_id, title, description, status, priority) VALUES
-- ('your-client-uuid-1', 'Website Redesign Project', 'Discussion about the new website layout and user experience improvements', 'open', 'high'),
-- ('your-client-uuid-1', 'SEO Optimization', 'Improving search engine rankings and organic traffic', 'pending', 'medium'),
-- ('your-client-uuid-1', 'Mobile App Development', 'Planning for the new mobile application', 'closed', 'low'),
-- ('your-client-uuid-2', 'Brand Identity Update', 'Refreshing the company brand and visual identity', 'open', 'high'),
-- ('your-client-uuid-2', 'Marketing Campaign', 'Q4 marketing campaign planning and execution', 'pending', 'medium'),
-- ('your-client-uuid-3', 'E-commerce Integration', 'Adding online store functionality to existing website', 'open', 'high'),
-- ('your-client-uuid-3', 'Payment Gateway Setup', 'Configuring secure payment processing', 'closed', 'medium'),
-- ('your-client-uuid-3', 'Inventory Management', 'Setting up automated inventory tracking', 'pending', 'low');

-- To use this script:
-- 1. First create test users through the signup form
-- 2. Check the clients table to get the actual client IDs
-- 3. Replace the placeholder UUIDs above with real client IDs
-- 4. Run the INSERT statements

-- You can also create an administrator user by signing up and then updating their role:
-- UPDATE public.profiles SET role = 'administrator' WHERE email = '<EMAIL>';
