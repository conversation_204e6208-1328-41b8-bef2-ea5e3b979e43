-- Insert administrator user (you'll need to sign up with this email first)
-- This is just for reference - the actual user creation happens through auth

-- Insert mock client users and data
-- Note: These will be created when users sign up, but here's the structure

-- Mock conversations data (will be inserted after client users are created)
-- For now, let's create some sample data assuming we have client users

-- You can run this after creating some test users through the app
-- INSERT INTO public.conversations (client_id, title, description, status, priority) VALUES
-- (client_uuid_1, 'Website Redesign Project', 'Discussion about the new website layout and features', 'open', 'high'),
-- (client_uuid_1, 'SEO Optimization', 'Improving search engine rankings', 'closed', 'medium'),
-- etc.
