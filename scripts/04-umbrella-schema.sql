-- Extend the existing schema for Umbrella CMS

-- Create agents table
CREATE TABLE IF NOT EXISTS public.agents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  company TEXT,
  role TEXT,
  phone TEXT,
  email TEXT,
  avatar_url TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create calls table (replacing conversations for this use case)
CREATE TABLE IF NOT EXISTS public.calls (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE NOT NULL,
  agent_id UUID REFERENCES public.agents(id) ON DELETE SET NULL,
  call_id TEXT UNIQUE NOT NULL, -- External call system ID
  contact_full_name TEXT,
  contact_phone TEXT,
  duration INTEGER DEFAULT 0, -- in seconds
  direction TEXT DEFAULT 'inbound' CHECK (direction IN ('inbound', 'outbound')),
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'finished', 'failed')),
  sentiment TEXT DEFAULT 'neutral' CHECK (sentiment IN ('positive', 'neutral', 'negative')),
  call_summary TEXT,
  call_transcript TEXT,
  audio_url TEXT,
  scheduled_date_time TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on new tables
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.calls ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agents
CREATE POLICY "Clients can view agents" ON public.agents
  FOR SELECT USING (true); -- All authenticated users can view agents

CREATE POLICY "Administrators can manage agents" ON public.agents
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'administrator'
    )
  );

-- RLS Policies for calls
CREATE POLICY "Clients can view own calls" ON public.calls
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.clients 
      WHERE id = calls.client_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Administrators can view all calls" ON public.calls
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'administrator'
    )
  );

CREATE POLICY "Administrators can manage calls" ON public.calls
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'administrator'
    )
  );

-- Drop the old conversations table and policies if they exist
DROP POLICY IF EXISTS "Clients can view own conversations" ON public.conversations;
DROP POLICY IF EXISTS "Administrators can view all conversations" ON public.conversations;
DROP POLICY IF EXISTS "Administrators can manage conversations" ON public.conversations;
DROP TABLE IF EXISTS public.conversations;
