"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/hooks/use-auth"
import { MessageSquare, Clock, CheckCircle, AlertCircle } from "lucide-react"
import Link from "next/link"
import { StaggerContainer, StaggerItem, SlideIn } from "@/components/ui/page-transition"
import { StatCardSkeleton, CardSkeleton } from "@/components/ui/skeleton"

type Conversation = {
  id: string
  title: string
  description: string | null
  status: "open" | "closed" | "pending"
  priority: "low" | "medium" | "high"
  created_at: string
}

type ClientData = {
  id: string
  company_name: string | null
  status: string
}

export function ClientDashboard() {
  const { profile } = useAuth()
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [clientData, setClientData] = useState<ClientData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (profile) {
      fetchClientData()
      fetchConversations()
    }
  }, [profile])

  const fetchClientData = async () => {
    try {
      const { data, error } = await supabase.from("clients").select("*").eq("user_id", profile?.id).single()

      if (error) throw error
      setClientData(data)
    } catch (error) {
      console.error("Error fetching client data:", error)
    }
  }

  const fetchConversations = async () => {
    try {
      const { data: clientData } = await supabase.from("clients").select("id").eq("user_id", profile?.id).single()

      if (clientData) {
        const { data, error } = await supabase
          .from("conversations")
          .select("*")
          .eq("client_id", clientData.id)
          .order("created_at", { ascending: false })

        if (error) throw error
        setConversations(data || [])
      }
    } catch (error) {
      console.error("Error fetching conversations:", error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open":
        return <AlertCircle className="h-4 w-4" />
      case "closed":
        return <CheckCircle className="h-4 w-4" />
      case "pending":
        return <Clock className="h-4 w-4" />
      default:
        return <MessageSquare className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-blue-100 text-blue-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header skeleton */}
        <SlideIn direction="up">
          <div className="space-y-2">
            <div className="h-8 w-64 bg-muted rounded animate-pulse" />
            <div className="h-4 w-96 bg-muted rounded animate-pulse" />
          </div>
        </SlideIn>

        {/* Stats cards skeleton */}
        <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StaggerItem><StatCardSkeleton /></StaggerItem>
          <StaggerItem><StatCardSkeleton /></StaggerItem>
          <StaggerItem><StatCardSkeleton /></StaggerItem>
          <StaggerItem><StatCardSkeleton /></StaggerItem>
        </StaggerContainer>

        {/* Recent conversations skeleton */}
        <StaggerItem>
          <CardSkeleton className="h-96" />
        </StaggerItem>
      </div>
    )
  }

  const openConversations = conversations.filter((c) => c.status === "open").length
  const closedConversations = conversations.filter((c) => c.status === "closed").length
  const pendingConversations = conversations.filter((c) => c.status === "pending").length

  return (
    <StaggerContainer className="space-y-6">
      <StaggerItem>
        <SlideIn direction="up">
          <div>
            <h1 className="text-3xl font-bold">Welcome back, {profile?.full_name}</h1>
            <p className="text-gray-600">
              {clientData?.company_name && `${clientData.company_name} • `}
              Here's your dashboard overview
            </p>
          </div>
        </SlideIn>
      </StaggerItem>

      {/* Dashboard Widgets */}
      <StaggerItem>
        <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" staggerDelay={0.1}>
          <StaggerItem>
            <Card className="hover:shadow-md transition-all duration-300 hover:scale-105">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{conversations.length}</div>
                <p className="text-xs text-muted-foreground">All time conversations</p>
              </CardContent>
            </Card>
          </StaggerItem>

          <StaggerItem>
            <Card className="hover:shadow-md transition-all duration-300 hover:scale-105">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Open Conversations</CardTitle>
                <AlertCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{openConversations}</div>
                <p className="text-xs text-muted-foreground">Currently active</p>
              </CardContent>
            </Card>
          </StaggerItem>

          <StaggerItem>
            <Card className="hover:shadow-md transition-all duration-300 hover:scale-105">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Conversations</CardTitle>
                <Clock className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{pendingConversations}</div>
                <p className="text-xs text-muted-foreground">Awaiting response</p>
              </CardContent>
            </Card>
          </StaggerItem>

          <StaggerItem>
            <Card className="hover:shadow-md transition-all duration-300 hover:scale-105">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <CheckCircle className="h-4 w-4 text-gray-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-600">{closedConversations}</div>
                <p className="text-xs text-muted-foreground">Closed conversations</p>
              </CardContent>
            </Card>
          </StaggerItem>
        </StaggerContainer>
      </StaggerItem>

      {/* Recent Conversations */}
      <StaggerItem>
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle>Recent Conversations</CardTitle>
            <CardDescription>Your latest conversations and their status</CardDescription>
          </CardHeader>
          <CardContent>
            {conversations.length === 0 ? (
              <SlideIn direction="up" delay={0.2}>
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No conversations yet</p>
                  <p className="text-sm text-gray-400">Your conversations will appear here</p>
                </div>
              </SlideIn>
            ) : (
              <StaggerContainer className="space-y-4" staggerDelay={0.05}>
                {conversations.slice(0, 5).map((conversation) => (
                  <StaggerItem key={conversation.id}>
                    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-4">
                        {getStatusIcon(conversation.status)}
                        <div>
                          <h3 className="font-medium">{conversation.title}</h3>
                          <p className="text-sm text-gray-600">{conversation.description}</p>
                          <p className="text-xs text-gray-400">{new Date(conversation.created_at).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getPriorityColor(conversation.priority)}>{conversation.priority}</Badge>
                        <Badge className={getStatusColor(conversation.status)}>{conversation.status}</Badge>
                        <Button asChild variant="outline" size="sm">
                          <Link href={`/conversations/${conversation.id}`}>View</Link>
                        </Button>
                      </div>
                    </div>
                  </StaggerItem>
                ))}
                {conversations.length > 5 && (
                  <StaggerItem>
                    <div className="text-center pt-4">
                      <Button asChild variant="outline">
                        <Link href="/conversations">View All Conversations</Link>
                      </Button>
                    </div>
                  </StaggerItem>
                )}
              </StaggerContainer>
            )}
          </CardContent>
        </Card>
      </StaggerItem>
    </StaggerContainer>
  )
}
