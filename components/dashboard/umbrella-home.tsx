"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Phone, List, ExternalLink, ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Separator } from "../ui/separator";
import { StaggerContainer, StaggerItem, FadeIn, SlideIn } from "@/components/ui/page-transition";

export function UmbrellaHome() {
	return (
		<StaggerContainer className="space-y-8">
			{/* Hero Section */}
			<StaggerItem>
				<FadeIn>
					<div className="relative bg-black rounded-lg overflow-hidden">
						<div className="absolute inset-0 lg:right-10">
							<Image
								src="/images/hero-ai-human.png"
								alt="Digital agents and human collaboration"
								fill
								className="object-contain object-right"
							/>
						</div>
						<div className="relative z-10 p-8 text-white bg-black/40">
							<div className="max-w-2xl">
								<SlideIn direction="up" delay={0.2}>
									<h1 className="text-4xl font-bold mb-4">
										Digital agents are always available, work fast and don't make
										human errors.
									</h1>
								</SlideIn>
								<SlideIn direction="up" delay={0.4}>
									<p className="text-lg opacity-90">It's not hard to work smart 😊</p>
								</SlideIn>
							</div>
						</div>
					</div>
				</FadeIn>
			</StaggerItem>

			{/* Welcome Section */}
			<StaggerItem>
				<div className="space-y-4">
					<div className="flex flex-col space-x-4">
						<SlideIn direction="left" delay={0.1}>
							<Image
								src="/logo.svg"
								alt="Umbrella CMS"
								width={128}
								height={128}
								className="w-32 h-32"
							/>
						</SlideIn>
						<SlideIn direction="right" delay={0.2}>
							<div>
								<h2 className="text-2xl font-bold">Home</h2>
								<p className="text-gray-600">
									Welcome to Umbrella CMS! This is your centralized dashboard for
									monitoring interactions between your digital agents and their
									contacts.
								</p>
							</div>
						</SlideIn>
					</div>
				</div>
			</StaggerItem>

			<StaggerItem>
				<div className="px-4">
					<Separator />
				</div>
			</StaggerItem>

			{/* Shortcuts and Resources */}
			<StaggerItem>
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
					{/* Shortcuts */}
					<div className="space-y-6 px-4">
						<SlideIn direction="left">
							<div>
								<h3 className="text-lg font-semibold mb-2">Shortcuts</h3>
								<p className="text-gray-600 text-sm mb-4">
									Use the links below to visit the modules.
								</p>
							</div>
						</SlideIn>

						<StaggerContainer staggerDelay={0.15} className="space-y-6">
							<StaggerItem>
								<Card className="hover:shadow-md transition-all duration-300 hover:scale-105">
									<CardContent className="p-6">
										<div className="flex items-center justify-between">
											<div className="flex items-center space-x-4">
												<div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
													<Phone className="h-5 w-5 text-red-600" />
												</div>
												<div>
													<h4 className="font-semibold text-red-600">
														Call Management
													</h4>
													<p className="text-sm text-gray-600">
														View and manage all inbound and outbound calls.
													</p>
												</div>
											</div>
											<Button
												asChild
												size="sm"
												className="rounded-full bg-red-600 hover:bg-red-500 w-8 h-8"
											>
												<Link href="/calls">
													<ArrowRight />
												</Link>
											</Button>
										</div>
									</CardContent>
								</Card>
							</StaggerItem>

							<StaggerItem>
								<Card className="hover:shadow-md transition-all duration-300 hover:scale-105">
									<CardContent className="p-6">
										<div className="flex items-center justify-between">
											<div className="flex items-center space-x-4">
												<div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
													<List className="h-5 w-5 text-red-600" />
												</div>
												<div>
													<h4 className="font-semibold text-red-600">Task List</h4>
													<p className="text-sm text-gray-600">
														Overview of tasks extracted from calls and assigned to
														team members.
													</p>
												</div>
											</div>
											<Button
												asChild
												size="sm"
												className="rounded-full bg-red-600 hover:bg-red-600/80 w-8 h-8"
											>
												<Link href="/calls">
													<ArrowRight />
												</Link>
											</Button>
										</div>
									</CardContent>
								</Card>
							</StaggerItem>
						</StaggerContainer>
					</div>

					{/* Resources */}
					<div className="w-full">
						<SlideIn direction="right">
							<Card style={{ width: "70%" }} className="hover:shadow-md transition-shadow">
								<CardContent className="p-8 h-max">
									<div className="space-y-6">
										<div>
											<h3 className="text-lg font-semibold">Resources</h3>
										</div>

										<StaggerContainer className="space-y-3" staggerDelay={0.1}>
											<StaggerItem>
												<Link
													href="https://3rdbase.io"
													className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 transition-colors"
												>
													<ExternalLink className="h-4 w-4" />
													<span>3rdbase</span>
												</Link>
											</StaggerItem>
											<StaggerItem>
												<Link
													href="https://3rdbase.io/ai-masterclass"
													className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 transition-colors"
												>
													<ExternalLink className="h-4 w-4" />
													<span>EU AI Act</span>
												</Link>
											</StaggerItem>
											<StaggerItem>
												<Link
													href="https://3rdbase.io/use-cases"
													className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 transition-colors"
												>
													<ExternalLink className="h-4 w-4" />
													<span>YouTube Playlist</span>
												</Link>
											</StaggerItem>
										</StaggerContainer>
									</div>
								</CardContent>
							</Card>
						</SlideIn>
					</div>
				</div>
			</StaggerItem>
		</StaggerContainer>
	);
}
