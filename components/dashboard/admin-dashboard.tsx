"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { Users, MessageSquare, Building, Activity } from "lucide-react"
import Link from "next/link"

type AdminStats = {
  totalClients: number
  totalConversations: number
  openConversations: number
  pendingConversations: number
}

type RecentActivity = {
  id: string
  title: string
  client_name: string
  status: string
  created_at: string
}

export function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats>({
    totalClients: 0,
    totalConversations: 0,
    openConversations: 0,
    pendingConversations: 0,
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAdminStats()
    fetchRecentActivity()
  }, [])

  const fetchAdminStats = async () => {
    try {
      // Get total clients
      const { count: clientCount } = await supabase.from("clients").select("*", { count: "exact", head: true })

      // Get total conversations
      const { count: conversationCount } = await supabase
        .from("conversations")
        .select("*", { count: "exact", head: true })

      // Get open conversations
      const { count: openCount } = await supabase
        .from("conversations")
        .select("*", { count: "exact", head: true })
        .eq("status", "open")

      // Get pending conversations
      const { count: pendingCount } = await supabase
        .from("conversations")
        .select("*", { count: "exact", head: true })
        .eq("status", "pending")

      setStats({
        totalClients: clientCount || 0,
        totalConversations: conversationCount || 0,
        openConversations: openCount || 0,
        pendingConversations: pendingCount || 0,
      })
    } catch (error) {
      console.error("Error fetching admin stats:", error)
    }
  }

  const fetchRecentActivity = async () => {
    try {
      const { data, error } = await supabase
        .from("conversations")
        .select(`
          id,
          title,
          status,
          created_at,
          clients!inner(
            company_name,
            profiles!inner(
              full_name
            )
          )
        `)
        .order("created_at", { ascending: false })
        .limit(5)

      if (error) throw error

      const formattedData =
        data?.map((item) => ({
          id: item.id,
          title: item.title,
          client_name:
            (item.clients as any)?.company_name || (item.clients as any)?.profiles?.full_name || "Unknown Client",
          status: item.status,
          created_at: item.created_at,
        })) || []

      setRecentActivity(formattedData)
    } catch (error) {
      console.error("Error fetching recent activity:", error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-blue-100 text-blue-800"
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Administrator Dashboard</h1>
        <p className="text-gray-600">System overview and management</p>
      </div>

      {/* Admin Stats Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClients}</div>
            <p className="text-xs text-muted-foreground">Registered clients</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalConversations}</div>
            <p className="text-xs text-muted-foreground">All conversations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Conversations</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.openConversations}</div>
            <p className="text-xs text-muted-foreground">Currently open</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Building className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pendingConversations}</div>
            <p className="text-xs text-muted-foreground">Needs attention</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Client Management</CardTitle>
            <CardDescription>Manage all client accounts</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/admin/clients">View All Clients</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Conversation Management</CardTitle>
            <CardDescription>View and manage all conversations</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/admin/conversations">View All Conversations</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Reports</CardTitle>
            <CardDescription>Generate system reports</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full bg-transparent">
              Generate Report
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest conversations across all clients</CardDescription>
        </CardHeader>
        <CardContent>
          {recentActivity.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No recent activity</p>
            </div>
          ) : (
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-medium">{activity.title}</h3>
                    <p className="text-sm text-gray-600">Client: {activity.client_name}</p>
                    <p className="text-xs text-gray-400">{new Date(activity.created_at).toLocaleDateString()}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(activity.status)}>{activity.status}</Badge>
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/conversations/${activity.id}`}>View</Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
