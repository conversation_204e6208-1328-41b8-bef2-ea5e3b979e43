"use client";

import type React from "react";

import { useAuth } from "@/hooks/use-auth";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

interface ProtectedRouteProps {
	children: React.ReactNode;
	requiredRole?: "client" | "administrator";
}

export function ProtectedRoute({
	children,
	requiredRole,
}: ProtectedRouteProps) {
	const { user, profile, loading } = useAuth();
	const router = useRouter();
	const pathname = usePathname();

	const isAdminPage = pathname.includes("/admin");

	const role = isAdminPage ? "administrator" : requiredRole;

	useEffect(() => {
		if (!loading) {
			if (!user) {
				router.push("/login");
				return;
			}

			if (role && profile?.role !== role) {
				router.push("/dashboard");
				return;
			}
		}
	}, [user, profile, loading, role, router]);

	if (loading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-lg">Loading...</div>
			</div>
		);
	}

	if (!user || (role && profile?.role !== role)) {
		return null;
	}

	return <>{children}</>;
}
