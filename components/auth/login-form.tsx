"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/hooks/use-auth"
import Link from "next/link"
import { StaggerContainer, StaggerItem, ScaleIn, SlideIn } from "@/components/ui/page-transition"
import { FormSkeleton } from "@/components/ui/skeleton"

export function LoginForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const { signIn } = useAuth()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    const { error } = await signIn(email, password)

    if (error) {
      setError(error.message)
    } else {
      router.push("/dashboard")
    }

    setLoading(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <ScaleIn>
          <Card className="w-full max-w-md">
            <CardHeader className="space-y-1">
              <div className="h-6 w-32 bg-muted rounded animate-pulse mx-auto" />
              <div className="h-4 w-48 bg-muted rounded animate-pulse mx-auto" />
            </CardHeader>
            <CardContent>
              <FormSkeleton fields={2} />
            </CardContent>
          </Card>
        </ScaleIn>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <ScaleIn>
        <Card className="w-full max-w-md hover:shadow-lg transition-shadow">
          <CardHeader className="space-y-1">
            <SlideIn direction="up" delay={0.1}>
              <CardTitle className="text-2xl font-bold text-center">Sign in</CardTitle>
            </SlideIn>
            <SlideIn direction="up" delay={0.2}>
              <CardDescription className="text-center">
                Enter your email and password to access your account
              </CardDescription>
            </SlideIn>
          </CardHeader>
          <CardContent>
            <StaggerContainer>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <StaggerItem>
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  </StaggerItem>
                )}

                <StaggerItem>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="transition-all focus:scale-105"
                    />
                  </div>
                </StaggerItem>

                <StaggerItem>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="transition-all focus:scale-105"
                    />
                  </div>
                </StaggerItem>

                <StaggerItem>
                  <Button
                    type="submit"
                    className="w-full hover:scale-105 transition-transform"
                    disabled={loading}
                  >
                    {loading ? "Signing in..." : "Sign in"}
                  </Button>
                </StaggerItem>
              </form>

              <StaggerItem>
                <div className="mt-4 text-center text-sm">
                  {"Don't have an account? "}
                  <Link href="/signup" className="text-blue-600 hover:underline transition-colors">
                    Sign up
                  </Link>
                </div>
              </StaggerItem>
            </StaggerContainer>
          </CardContent>
        </Card>
      </ScaleIn>
    </div>
  )
}
