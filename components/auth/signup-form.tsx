"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/hooks/use-auth"
import Link from "next/link"
import { StaggerContainer, StaggerItem, ScaleIn, SlideIn } from "@/components/ui/page-transition"
import { FormSkeleton } from "@/components/ui/skeleton"

export function SignupForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [fullName, setFullName] = useState("")
  const [role, setRole] = useState<"client" | "administrator">("client")
  const [companyName, setCompanyName] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const { signUp } = useAuth()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    const userData = {
      full_name: fullName,
      role,
      ...(role === "client" && { company_name: companyName }),
    }

    const { error } = await signUp(email, password, userData)

    if (error) {
      setError(error.message)
    } else {
      setSuccess(true)
      setTimeout(() => {
        router.push("/login")
      }, 2000)
    }

    setLoading(false)
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <ScaleIn>
          <Card className="w-full max-w-md">
            <CardContent className="pt-6">
              <SlideIn direction="up">
                <Alert>
                  <AlertDescription>
                    Account created successfully! Please check your email to confirm your account. Redirecting to login...
                  </AlertDescription>
                </Alert>
              </SlideIn>
            </CardContent>
          </Card>
        </ScaleIn>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <ScaleIn>
          <Card className="w-full max-w-md">
            <CardHeader className="space-y-1">
              <div className="h-6 w-40 bg-muted rounded animate-pulse mx-auto" />
              <div className="h-4 w-48 bg-muted rounded animate-pulse mx-auto" />
            </CardHeader>
            <CardContent>
              <FormSkeleton fields={5} />
            </CardContent>
          </Card>
        </ScaleIn>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <ScaleIn>
        <Card className="w-full max-w-md hover:shadow-lg transition-shadow">
          <CardHeader className="space-y-1">
            <SlideIn direction="up" delay={0.1}>
              <CardTitle className="text-2xl font-bold text-center">Create Account</CardTitle>
            </SlideIn>
            <SlideIn direction="up" delay={0.2}>
              <CardDescription className="text-center">Sign up for a new account</CardDescription>
            </SlideIn>
          </CardHeader>
          <CardContent>
            <StaggerContainer>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <StaggerItem>
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  </StaggerItem>
                )}

                <StaggerItem>
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      type="text"
                      placeholder="Enter your full name"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      required
                      className="transition-all focus:scale-105"
                    />
                  </div>
                </StaggerItem>

                <StaggerItem>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="transition-all focus:scale-105"
                    />
                  </div>
                </StaggerItem>

                <StaggerItem>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      minLength={6}
                      className="transition-all focus:scale-105"
                    />
                  </div>
                </StaggerItem>

                <StaggerItem>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Select value={role} onValueChange={(value: "client" | "administrator") => setRole(value)}>
                      <SelectTrigger className="transition-all focus:scale-105">
                        <SelectValue placeholder="Select your role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="client">Client</SelectItem>
                        <SelectItem value="administrator">Administrator</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </StaggerItem>

                {role === "client" && (
                  <StaggerItem>
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input
                        id="companyName"
                        type="text"
                        placeholder="Enter your company name"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                        className="transition-all focus:scale-105"
                      />
                    </div>
                  </StaggerItem>
                )}

                <StaggerItem>
                  <Button
                    type="submit"
                    className="w-full hover:scale-105 transition-transform"
                    disabled={loading}
                  >
                    {loading ? "Creating Account..." : "Create Account"}
                  </Button>
                </StaggerItem>
              </form>

              <StaggerItem>
                <div className="mt-4 text-center text-sm">
                  Already have an account?{" "}
                  <Link href="/login" className="text-blue-600 hover:underline transition-colors">
                    Sign in
                  </Link>
                </div>
              </StaggerItem>
            </StaggerContainer>
          </CardContent>
        </Card>
      </ScaleIn>
    </div>
  )
}
