"use client";

import { motion, AnimatePresence } from "framer-motion";
import { ReactNode } from "react";

interface PageTransitionProps {
	children: ReactNode;
	className?: string;
}

const pageVariants = {
	initial: {
		opacity: 0,
		y: 20,
	},
	in: {
		opacity: 1,
		y: 0,
	},
	out: {
		opacity: 0,
		y: -20,
	},
};

const pageTransition = {
	type: "tween",
	ease: "anticipate",
	duration: 0.4,
};

export function PageTransition({ children, className }: PageTransitionProps) {
	return (
		<motion.div
			className={className}
			initial="initial"
			animate="in"
			exit="out"
			variants={pageVariants}
			transition={pageTransition}
		>
			{children}
		</motion.div>
	);
}

// Stagger container for animating multiple children
export function StaggerContainer({
	children,
	className,
	staggerDelay = 0.1,
}: {
	children: ReactNode;
	className?: string;
	staggerDelay?: number;
}) {
	return (
		<motion.div
			className={className}
			initial="hidden"
			animate="visible"
			variants={{
				hidden: { opacity: 0 },
				visible: {
					opacity: 1,
					transition: {
						staggerChildren: staggerDelay,
					},
				},
			}}
		>
			{children}
		</motion.div>
	);
}

// Stagger container for animating multiple children
export function StaggerContainerTBody({
	children,
	className,
	staggerDelay = 0.1,
}: {
	children: ReactNode;
	className?: string;
	staggerDelay?: number;
}) {
	return (
		<motion.tbody
			className={className}
			initial="hidden"
			animate="visible"
			variants={{
				hidden: { opacity: 0 },
				visible: {
					opacity: 1,
					transition: {
						staggerChildren: staggerDelay,
					},
				},
			}}
		>
			{children}
		</motion.tbody>
	);
}

// Individual stagger item
export function StaggerItem({
	children,
	className,
}: {
	children: ReactNode;
	className?: string;
}) {
	return (
		<motion.div
			className={className}
			variants={{
				hidden: { opacity: 0, y: 20 },
				visible: {
					opacity: 1,
					y: 0,
					transition: {
						duration: 0.4,
						ease: "easeOut",
					},
				},
			}}
		>
			{children}
		</motion.div>
	);
}

// Individual stagger item
export function StaggerItemTr({
	children,
	className,
	onClick,
}: {
	children: ReactNode;
	className?: string;
	onClick?: () => void;
}) {
	return (
		<motion.tr
			className={className}
			variants={{
				hidden: { opacity: 0, y: 20 },
				visible: {
					opacity: 1,
					y: 0,
					transition: {
						duration: 0.4,
						ease: "easeOut",
					},
				},
			}}
			onClick={onClick}
		>
			{children}
		</motion.tr>
	);
}

// Fade in animation
export function FadeIn({
	children,
	className,
	delay = 0,
}: {
	children: ReactNode;
	className?: string;
	delay?: number;
}) {
	return (
		<motion.div
			className={className}
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.5, delay }}
		>
			{children}
		</motion.div>
	);
}

// Slide in from direction
export function SlideIn({
	children,
	className,
	direction = "up",
	delay = 0,
}: {
	children: ReactNode;
	className?: string;
	direction?: "up" | "down" | "left" | "right";
	delay?: number;
}) {
	const directionOffset = {
		up: { y: 20 },
		down: { y: -20 },
		left: { x: 20 },
		right: { x: -20 },
	};

	return (
		<motion.div
			className={className}
			initial={{ opacity: 0, ...directionOffset[direction] }}
			animate={{ opacity: 1, x: 0, y: 0 }}
			transition={{ duration: 0.4, delay, ease: "easeOut" }}
		>
			{children}
		</motion.div>
	);
}

// Slide in from direction
export function SlideInTHead({
	children,
	className,
	direction = "up",
	delay = 0,
}: {
	children: ReactNode;
	className?: string;
	direction?: "up" | "down" | "left" | "right";
	delay?: number;
}) {
	const directionOffset = {
		up: { y: 20 },
		down: { y: -20 },
		left: { x: 20 },
		right: { x: -20 },
	};

	return (
		<motion.thead
			className={className}
			initial={{ opacity: 0, ...directionOffset[direction] }}
			animate={{ opacity: 1, x: 0, y: 0 }}
			transition={{ duration: 0.4, delay, ease: "easeOut" }}
		>
			{children}
		</motion.thead>
	);
}

// Scale animation
export function ScaleIn({
	children,
	className,
	delay = 0,
}: {
	children: ReactNode;
	className?: string;
	delay?: number;
}) {
	return (
		<motion.div
			className={className}
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			transition={{ duration: 0.3, delay, ease: "easeOut" }}
		>
			{children}
		</motion.div>
	);
}
