"use client"

import { useEffect, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { supabase } from "@/lib/supabase"
import type { Agent } from "@/lib/types"
import { Search, Plus } from "lucide-react"
import Image from "next/image"

export function AgentsGrid() {
  const [agents, setAgents] = useState<Agent[]>([])
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    fetchAgents()
  }, [])

  useEffect(() => {
    filterAgents()
  }, [agents, searchTerm])

  const fetchAgents = async () => {
    try {
      const { data, error } = await supabase.from("agents").select("*").eq("status", "active").order("name")

      if (error) throw error
      setAgents(data || [])
    } catch (error) {
      console.error("Error fetching agents:", error)
    } finally {
      setLoading(false)
    }
  }

  const filterAgents = () => {
    let filtered = agents

    if (searchTerm) {
      filtered = filtered.filter(
        (agent) =>
          agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          agent.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          agent.role?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    setFilteredAgents(filtered)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Agent Management</h1>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-sm text-gray-600">Active Agents</span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            Filter
          </Button>
          <Button variant="outline" size="sm">
            Sort
          </Button>
          <Button variant="outline" size="sm">
            <Search className="h-4 w-4" />
          </Button>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Add record
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search agents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Agents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {filteredAgents.map((agent) => (
          <Card key={agent.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="aspect-square relative">
              <Image
                src={agent.avatar_url || "/placeholder.svg?height=300&width=300"}
                alt={agent.name}
                fill
                className="object-cover"
              />
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-2">{agent.name}</h3>
              <div className="space-y-1 text-sm text-gray-600">
                {agent.company && <p>{agent.company}</p>}
                {agent.role && <p>{agent.role}</p>}
                {agent.phone && <p>{agent.phone}</p>}
              </div>
              <div className="mt-3 flex items-center">
                <div className="w-4 h-3 bg-gray-300 rounded-sm mr-2"></div>
                <div className="w-4 h-3 bg-gray-300 rounded-sm"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAgents.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">{searchTerm ? "No agents match your search" : "No agents found"}</p>
        </div>
      )}
    </div>
  )
}
