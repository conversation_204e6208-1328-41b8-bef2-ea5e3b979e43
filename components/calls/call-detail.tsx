"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/hooks/use-auth"
import type { Call } from "@/lib/types"
import { ArrowLeft, Play, Pause } from "lucide-react"

export function CallDetail() {
  const params = useParams()
  const router = useRouter()
  const { profile } = useAuth()
  const [call, setCall] = useState<Call | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [isPlaying, setIsPlaying] = useState(false)

  useEffect(() => {
    if (params.id && profile) {
      fetchCallDetails()
    }
  }, [params.id, profile])

  const fetchCallDetails = async () => {
    try {
      const { data, error } = await supabase
        .from("calls")
        .select(`
          *,
          agents(name, company),
          clients!inner(
            id,
            company_name,
            profiles!inner(
              full_name,
              email
            )
          )
        `)
        .eq("call_id", params.id)
        .single()

      if (error) throw error

      // Check if user has permission to view this call
      if (profile?.role === "client") {
        const { data: clientData } = await supabase.from("clients").select("id").eq("user_id", profile.id).single()

        if (clientData?.id !== (data.clients as any).id) {
          setError("You do not have permission to view this call")
          return
        }
      }

      const formattedCall = {
        ...data,
        agent: data.agents
          ? {
              id: data.agent_id,
              name: data.agents.name,
              company: data.agents.company,
            }
          : null,
        client: {
          id: (data.clients as any).id,
          company_name: (data.clients as any).company_name,
          user: {
            full_name: (data.clients as any).profiles.full_name,
            email: (data.clients as any).profiles.email,
          },
        },
      }

      setCall(formattedCall)
    } catch (error) {
      console.error("Error fetching call details:", error)
      setError("Failed to load call details")
    } finally {
      setLoading(false)
    }
  }

  const togglePlayback = () => {
    setIsPlaying(!isPlaying)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <Button onClick={() => router.back()} className="mt-4">
          Go Back
        </Button>
      </div>
    )
  }

  if (!call) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Call not found</p>
        <Button onClick={() => router.back()} className="mt-4">
          Go Back
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to List
        </Button>
      </div>

      <div>
        <h1 className="text-3xl font-bold">{call.call_id}</h1>
      </div>

      {/* Call Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardContent className="pt-6 space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">call_scheduled_date_time</h3>
                <p className="text-gray-600">–</p>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">payload_title</h3>
                <p className="text-gray-600">–</p>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">contact_full_name</h3>
                <p className="text-gray-600">{call.contact_full_name || "–"}</p>
              </div>
            </CardContent>
          </Card>

          {/* Audio Player */}
          <Card>
            <CardHeader>
              <CardTitle>call_player</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center p-8">
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={togglePlayback}
                  className="w-16 h-16 rounded-full border-2 border-gray-300 hover:border-gray-400"
                >
                  {isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
                </Button>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>00:00</span>
                <div className="flex-1 mx-4">
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div className="bg-gray-400 h-1 rounded-full" style={{ width: "0%" }}></div>
                  </div>
                </div>
                <span>
                  00:
                  {Math.floor(call.duration / 60)
                    .toString()
                    .padStart(2, "0")}
                </span>
              </div>
              <div className="mt-2 text-center text-sm text-gray-500">recording.wav</div>
            </CardContent>
          </Card>

          {/* Call Summary */}
          <Card>
            <CardHeader>
              <CardTitle>call_summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-gray-700 leading-relaxed">
                  {call.call_summary ||
                    "The user inquired about the baggage allowance for their flight. Maryam AI Nuami from Emirates customer service offered to assist with the information needed regarding baggage policies. The conversation is focused on understanding the rules and limitations related to luggage. The user seeks clarity on what is permitted for their journey. This interaction shows the user's intent to gather important details for their travel plans."}
                </p>
              </div>

              <div className="pt-4 border-t">
                <p className="text-gray-700 leading-relaxed">
                  De gebruiker vroeg naar de bagagevergoeding voor hun vlucht. Maryam AI Nuami van de Emirates
                  klantenservice bood aan om te helpen met de benodigde informatie over bagagebeleid. Het gesprek
                  richtte zich op het begrijpen van de regels en beperkingen met betrekking tot bagage. De gebruiker
                  zoekt helderheid over wat is toegestaan voor hun reis. Deze interactie toont de intentie van de
                  gebruiker om belangrijke details voor hun reisplannen te verzamelen.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Transcript */}
          <Card>
            <CardHeader>
              <CardTitle>call_transcript</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-gray-700">
                    <strong>Agent:</strong> Good morning, Emirates customer service, this is Maryam Al Nuami speaking.
                    How may I assist you with your flight today?
                  </p>
                </div>
                <div>
                  <p className="text-gray-700">
                    <strong>User:</strong> Tell me something about the baggage allowance.
                  </p>
                </div>
                {call.call_transcript && (
                  <div>
                    <p className="text-gray-700 whitespace-pre-wrap">{call.call_transcript}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Comments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                </div>
                <p className="text-gray-600 mb-2">Start a conversation</p>
                <p className="text-sm text-gray-500">
                  Ask questions and collaborate with your team - directly in Airtable.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
