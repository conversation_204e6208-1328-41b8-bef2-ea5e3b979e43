"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/hooks/use-auth";
import type { Call } from "@/lib/types";
import { ArrowLeft, Play, Pause, MessageSquare } from "lucide-react";

export function CallDetailPage() {
	const params = useParams();
	const router = useRouter();
	const { profile } = useAuth();
	const [call, setCall] = useState<Call | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState("");
	const [isPlaying, setIsPlaying] = useState(false);

	useEffect(() => {
		if (params.id && profile) {
			fetchCallDetails();
		}
	}, [params.id, profile]);

	const fetchCallDetails = async () => {
		try {
			const { data, error } = await supabase
				.from("calls")
				.select(
					`
          *,
          agents(name, company, role),
          clients!inner(
            id,
            company_name,
            profiles!inner(
              full_name,
              email
            )
          )
        `
				)
				.eq("call_id", params.id)
				.single();

			if (error) throw error;

			// Check if user has permission to view this call
			if (profile?.role === "client") {
				const { data: clientData } = await supabase
					.from("clients")
					.select("id")
					.eq("user_id", profile.id)
					.single();

				if (clientData?.id !== (data.clients as any).id) {
					setError("You do not have permission to view this call");
					return;
				}
			}

			const formattedCall = {
				...data,
				agent: data.agents
					? {
							id: data.agent_id,
							name: data.agents.name,
							company: data.agents.company,
							role: data.agents.role,
					  }
					: null,
				client: {
					id: (data.clients as any).id,
					company_name: (data.clients as any).company_name,
					user: {
						full_name: (data.clients as any).profiles.full_name,
						email: (data.clients as any).profiles.email,
					},
				},
			};

			setCall(formattedCall);
		} catch (error) {
			console.error("Error fetching call details:", error);
			setError("Failed to load call details");
		} finally {
			setLoading(false);
		}
	};

	const togglePlayback = () => {
		setIsPlaying(!isPlaying);
	};

	const formatDuration = (seconds: number) => {
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
			.toString()
			.padStart(2, "0")}`;
	};

	const formatLabels = (value: string) => {
		return (value.charAt(0).toUpperCase() + value.slice(1)).replace("_", " ");
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "finished":
				return "bg-green-100 text-green-800";
			case "in_progress":
				return "bg-yellow-100 text-yellow-800";
			case "scheduled":
				return "bg-blue-100 text-blue-800";
			case "failed":
				return "bg-red-100 text-red-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getSentimentColor = (sentiment: string) => {
		switch (sentiment) {
			case "positive":
				return "bg-green-100 text-green-800";
			case "negative":
				return "bg-red-100 text-red-800";
			case "neutral":
				return "bg-purple-100 text-purple-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getDirectionColor = (direction: string) => {
		switch (direction) {
			case "inbound":
				return "bg-blue-100 text-blue-800";
			case "outbound":
				return "bg-pink-100 text-pink-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-lg">Loading...</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-8">
				<p className="text-red-600">{error}</p>
				<Button onClick={() => router.back()} className="mt-4">
					Go Back
				</Button>
			</div>
		);
	}

	if (!call) {
		return (
			<div className="text-center py-8">
				<p className="text-gray-600">Call not found</p>
				<Button onClick={() => router.back()} className="mt-4">
					Go Back
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center space-x-4">
				<Button variant="ghost" onClick={() => router.push("/calls")}>
					<ArrowLeft className="h-4 w-4 mr-2" />
					Back to List
				</Button>
			</div>

			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold">{call.call_id}</h1>
				<div className="flex items-center space-x-2">
					<Badge className={getStatusColor(call.status)}>
						{formatLabels(call.status)}
					</Badge>
					<Badge className={getSentimentColor(call.sentiment)}>
						{formatLabels(call.sentiment)}
					</Badge>
					<Badge className={getDirectionColor(call.direction)}>
						{formatLabels(call.direction)}
					</Badge>
				</div>
			</div>

			{/* Main Content */}
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				<div className="lg:col-span-2 space-y-6">
					{/* Basic Info */}
					<Card>
						<CardContent className="pt-6 space-y-4">
							<div>
								<h3 className="font-medium text-gray-900">Scheduled Date</h3>
								<p className="text-gray-600">
									{call.scheduled_date_time
										? new Date(call.scheduled_date_time).toLocaleString()
										: "–"}
								</p>
							</div>

							<div>
								<h3 className="font-medium text-gray-900">Title</h3>
								<p className="text-gray-600">–</p>
							</div>

							<div>
								<h3 className="font-medium text-gray-900">Contact Name</h3>
								<p className="text-gray-600">{call.contact_full_name || "–"}</p>
							</div>

							<div>
								<h3 className="font-medium text-gray-900">Phone Number</h3>
								<p className="text-gray-600">{call.contact_phone || "–"}</p>
							</div>

							<div>
								<h3 className="font-medium text-gray-900">Agent</h3>
								<p className="text-gray-600">
									{call.agent
										? `${call.agent.name} (${call.agent.company})`
										: "–"}
								</p>
							</div>

							<div>
								<h3 className="font-medium text-gray-900">Duration</h3>
								<p className="text-gray-600">{formatDuration(call.duration)}</p>
							</div>
						</CardContent>
					</Card>

					{/* Audio Player */}
					<Card>
						<CardHeader>
							<CardTitle>Audio Recording</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="flex items-center justify-center p-8">
								<Button
									variant="ghost"
									size="lg"
									onClick={togglePlayback}
									className="w-16 h-16 rounded-full border-2 border-gray-300 hover:border-gray-400"
								>
									{isPlaying ? (
										<Pause className="h-8 w-8" />
									) : (
										<Play className="h-8 w-8" />
									)}
								</Button>
							</div>
							<div className="flex items-center justify-between text-sm text-gray-500">
								<span>00:00</span>
								<div className="flex-1 mx-4">
									<div className="w-full bg-gray-200 rounded-full h-1">
										<div
											className="bg-gray-400 h-1 rounded-full"
											style={{ width: "0%" }}
										></div>
									</div>
								</div>
								<span>{formatDuration(call.duration)}</span>
							</div>
							<div className="mt-2 text-center text-sm text-gray-500">
								recording.wav
							</div>
						</CardContent>
					</Card>

					{/* Call Summary */}
					<Card>
						<CardHeader>
							<CardTitle>Call Summary</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div>
								<p className="text-gray-700 leading-relaxed">
									{call.call_summary || "No summary available for this call."}
								</p>
							</div>
						</CardContent>
					</Card>

					{/* Transcript */}
					<Card>
						<CardHeader>
							<CardTitle>Call Transcript</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								{call.call_transcript ? (
									<div className="whitespace-pre-wrap text-gray-700">
										{call.call_transcript}
									</div>
								) : (
									<div className="space-y-4">
										<div>
											<p className="text-gray-700">
												<strong>Agent:</strong> Good morning, this is{" "}
												{call.agent?.name || "our agent"} speaking. How may I
												assist you today?
											</p>
										</div>
										<div>
											<p className="text-gray-700">
												<strong>Customer:</strong>{" "}
												{call.contact_full_name || "Customer"} here. I need some
												assistance...
											</p>
										</div>
										<div className="text-sm text-gray-500 italic">
											Full transcript not available for this call.
										</div>
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Sidebar */}
				<div>
					<Card>
						<CardHeader>
							<CardTitle>Comments</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-center py-8">
								<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
									<MessageSquare className="w-8 h-8 text-gray-400" />
								</div>
								<p className="text-gray-600 mb-2">Start a conversation</p>
								<p className="text-sm text-gray-500">
									Ask questions and collaborate with your team - directly in the
									system.
								</p>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
