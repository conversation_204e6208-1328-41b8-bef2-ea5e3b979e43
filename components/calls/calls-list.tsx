"use client";

import { useEffect, useState } from "react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/hooks/use-auth";
import type { Call } from "@/lib/types";
import { Play, Search, Filter } from "lucide-react";
import {
	StaggerContainer,
	StaggerItem,
	SlideIn,
	SlideInTHead,
	StaggerItemTr,
	StaggerContainerTBody,
} from "@/components/ui/page-transition";
import { TableSkeleton, CardSkeleton } from "@/components/ui/skeleton";
import { useRouter } from "next/navigation";

export function CallsList() {
	const { profile } = useAuth();
	const [calls, setCalls] = useState<Call[]>([]);
	const [filteredCalls, setFilteredCalls] = useState<Call[]>([]);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState<string>("all");

	const router = useRouter();

	useEffect(() => {
		if (profile) {
			fetchCalls();
		}
	}, [profile]);

	useEffect(() => {
		filterCalls();
	}, [calls, searchTerm, statusFilter]);

	const fetchCalls = async () => {
		try {
			let query = supabase
				.from("calls")
				.select(
					`
          *,
          agents(name, company),
          clients!inner(
            id,
            company_name,
            profiles!inner(
              full_name,
              email
            )
          )
        `
				)
				.order("created_at", { ascending: false });

			// If client, only show their calls
			if (profile?.role === "client") {
				const { data: clientData } = await supabase
					.from("clients")
					.select("id")
					.eq("user_id", profile.id)
					.single();
				if (clientData) {
					query = query.eq("client_id", clientData.id);
				}
			}

			const { data, error } = await query;

			if (error) throw error;

			const formattedCalls =
				data?.map((call) => ({
					...call,
					agent: call.agents
						? {
								id: call.agent_id,
								name: call.agents.name,
								company: call.agents.company,
						  }
						: null,
					client: {
						id: (call.clients as any).id,
						company_name: (call.clients as any).company_name,
						user: {
							full_name: (call.clients as any).profiles.full_name,
							email: (call.clients as any).profiles.email,
						},
					},
				})) || [];

			setCalls(formattedCalls);
		} catch (error) {
			console.error("Error fetching calls:", error);
		} finally {
			setLoading(false);
		}
	};

	const filterCalls = () => {
		let filtered = calls;

		if (searchTerm) {
			filtered = filtered.filter(
				(call) =>
					call.contact_full_name
						?.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					call.call_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
					call.agent?.name?.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}

		if (statusFilter !== "all") {
			filtered = filtered.filter((call) => call.status === statusFilter);
		}

		setFilteredCalls(filtered);
	};

	const formatLabels = (value: string) => {
		return (value.charAt(0).toUpperCase() + value.slice(1)).replace("_", " ");
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "finished":
				return "bg-green-100 text-green-800";
			case "in_progress":
				return "bg-yellow-100 text-yellow-800";
			case "scheduled":
				return "bg-blue-100 text-blue-800";
			case "failed":
				return "bg-red-100 text-red-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getSentimentColor = (sentiment: string) => {
		switch (sentiment) {
			case "positive":
				return "bg-green-100 text-green-800";
			case "negative":
				return "bg-red-100 text-red-800";
			case "neutral":
				return "bg-purple-100 text-purple-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getDirectionColor = (direction: string) => {
		switch (direction) {
			case "inbound":
				return "bg-blue-100 text-blue-800";
			case "outbound":
				return "bg-pink-100 text-pink-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const formatDuration = (seconds: number) => {
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
	};

	if (loading) {
		return (
			<div className="space-y-6">
				{/* Header skeleton */}
				<SlideIn direction="up">
					<div className="space-y-2">
						<div className="h-8 w-32 bg-muted rounded animate-pulse" />
						<div className="h-4 w-48 bg-muted rounded animate-pulse" />
					</div>
				</SlideIn>

				{/* Controls skeleton */}
				<StaggerContainer className="flex items-center justify-between">
					<StaggerItem>
						<div className="flex items-center space-x-2">
							<div className="h-8 w-16 bg-muted rounded animate-pulse" />
							<div className="h-8 w-16 bg-muted rounded animate-pulse" />
							<div className="h-8 w-16 bg-muted rounded animate-pulse" />
							<div className="h-8 w-8 bg-muted rounded animate-pulse" />
						</div>
					</StaggerItem>
				</StaggerContainer>

				{/* Filters skeleton */}
				<StaggerItem>
					<CardSkeleton className="h-32" />
				</StaggerItem>

				{/* Table skeleton */}
				<StaggerItem>
					<Card>
						<CardHeader>
							<div className="h-6 w-24 bg-muted rounded animate-pulse" />
							<div className="h-4 w-32 bg-muted rounded animate-pulse" />
						</CardHeader>
						<CardContent>
							<TableSkeleton rows={8} />
						</CardContent>
					</Card>
				</StaggerItem>
			</div>
		);
	}

	return (
		<StaggerContainer className="space-y-6">
			<StaggerItem>
				<SlideIn direction="up">
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-3xl font-bold">Calls</h1>
							<div className="flex items-center space-x-4 mt-2">
								<span className="text-sm text-gray-600">List</span>
							</div>
						</div>
						<div className="flex items-center space-x-2">
							<Button
								variant="outline"
								size="sm"
								className="hover:scale-105 transition-transform"
							>
								Group
							</Button>
							<Button
								variant="outline"
								size="sm"
								className="hover:scale-105 transition-transform"
							>
								Filter
							</Button>
							<Button
								variant="outline"
								size="sm"
								className="hover:scale-105 transition-transform"
							>
								Sort
							</Button>
							<Button
								variant="outline"
								size="sm"
								className="hover:scale-105 transition-transform"
							>
								<Search className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</SlideIn>
			</StaggerItem>

			{/* Search and Filter */}
			<StaggerItem>
				<Card className="hover:shadow-md transition-shadow">
					<CardContent className="pt-6">
						<div className="flex flex-col sm:flex-row gap-4">
							<div className="relative flex-1">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
								<Input
									placeholder="Search calls..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-10"
								/>
							</div>
							<div className="flex items-center gap-2">
								<Filter className="h-4 w-4 text-gray-400" />
								<select
									value={statusFilter}
									onChange={(e) => setStatusFilter(e.target.value)}
									className="border border-gray-300 rounded-md px-3 py-2 text-sm"
								>
									<option value="all">All Status</option>
									<option value="scheduled">Scheduled</option>
									<option value="in_progress">In Progress</option>
									<option value="finished">Finished</option>
									<option value="failed">Failed</option>
								</select>
							</div>
						</div>
					</CardContent>
				</Card>
			</StaggerItem>

			{/* Calls Table */}
			<StaggerItem>
				<Card className="hover:shadow-md transition-shadow">
					<CardHeader>
						<CardTitle>{filteredCalls.length} Calls</CardTitle>
						<CardDescription>
							{searchTerm || statusFilter !== "all"
								? "Filtered results"
								: "All calls"}
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="overflow-x-auto">
							<table className="w-full">
								<SlideInTHead direction="up" delay={0.1}>
									<tr className="border-b text-left text-sm text-gray-500">
										<th className="pb-3 font-medium">Created Time</th>
										<th className="pb-3 font-medium">Recording</th>
										<th className="pb-3 font-medium">Duration</th>
										<th className="pb-3 font-medium">Sentiment</th>
										<th className="pb-3 font-medium">Contact Name</th>
										<th className="pb-3 font-medium">Status</th>
										<th className="pb-3 font-medium">Direction</th>
										<th className="pb-3 font-medium">Scheduled Date</th>
									</tr>
								</SlideInTHead>
								<StaggerContainerTBody className="text-sm" staggerDelay={0.02}>
									{filteredCalls.map((call, index) => (
										<StaggerItemTr
											key={call.id}
											className="border-b hover:bg-gray-50 cursor-pointer transition-colors"
											onClick={() => router.push(`/calls/${call.call_id}`)}
										>
											<td className="py-3">
												<div>
													<div className="font-medium">
														{new Date(call.created_at).toLocaleDateString(
															"en-GB"
														)}
													</div>
													<div className="text-gray-500">
														{new Date(call.created_at).toLocaleTimeString(
															"en-GB",
															{
																hour: "2-digit",
																minute: "2-digit",
															}
														)}
													</div>
													<div className="text-xs text-gray-400">CEST</div>
												</div>
											</td>
											<td className="py-3">
												<div className="flex items-center space-x-2">
													<Button
														variant="ghost"
														size="sm"
														className="p-1 hover:scale-110 transition-transform"
													>
														<Play className="h-4 w-4" />
													</Button>
													<div className="w-2 h-2 bg-gray-400 rounded-full"></div>
												</div>
											</td>
											<td className="py-3 font-medium">
												{formatDuration(call.duration)}
											</td>
											<td className="py-3">
												<Badge className={getSentimentColor(call.sentiment)}>
													{formatLabels(call.sentiment)}
												</Badge>
											</td>
											<td className="py-3 font-medium">
												{call.contact_full_name || "–"}
											</td>
											<td className="py-3">
												<Badge className={getStatusColor(call.status)}>
													{formatLabels(call.status)}
												</Badge>
											</td>
											<td className="py-3">
												<Badge className={getDirectionColor(call.direction)}>
													{formatLabels(call.direction)}
												</Badge>
											</td>
											<td className="py-3 text-gray-500">–</td>
										</StaggerItemTr>
									))}
								</StaggerContainerTBody>
							</table>
						</div>
					</CardContent>
				</Card>
			</StaggerItem>
		</StaggerContainer>
	);
}
