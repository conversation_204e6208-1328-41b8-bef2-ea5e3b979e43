"use client";

import { useEffect, useState } from "react";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/hooks/use-auth";
import type { CallStats } from "@/lib/types";
import {
	<PERSON><PERSON>hart,
	Pie,
	Cell,
	ResponsiveContainer,
	Legend,
	Tooltip,
} from "recharts";

const SENTIMENT_COLORS = {
	positive: "#22c55e",
	neutral: "#a855f7",
	negative: "#ef4444",
};

const DIRECTION_COLORS = {
	inbound: "#3b82f6",
	outbound: "#ec4899",
};

const CustomTooltip = ({ active, payload }: any) => {
	if (active && payload && payload.length) {
		const data = payload[0];
		return (
			<div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
				<p className="font-medium capitalize">{data.name}</p>
				<p className="text-sm text-gray-600">Count: {data.value}</p>
			</div>
		);
	}
	return null;
};

export function CallStatsDashboard() {
	const { profile } = useAuth();
	const [stats, setStats] = useState<CallStats>({
		scheduled: 0,
		finished: 0,
		in_progress: 0,
		failed: 0,
		total: 0,
		sentimentStats: { positive: 0, neutral: 0, negative: 0 },
		directionStats: { inbound: 0, outbound: 0 },
	});
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (profile) {
			fetchCallStats();
		}
	}, [profile]);

	const fetchCallStats = async () => {
		try {
			let query = supabase.from("calls").select("status, sentiment, direction");

			// If client, only show their calls
			if (profile?.role === "client") {
				const { data: clientData } = await supabase
					.from("clients")
					.select("id")
					.eq("user_id", profile.id)
					.single();
				if (clientData) {
					query = query.eq("client_id", clientData.id);
				}
			}

			const { data, error } = await query;

			if (error) throw error;

			const calls = data || [];
			const total = calls.length;

			// Calculate status stats
			const scheduled = calls.filter((c) => c.status === "scheduled").length;
			const finished = calls.filter((c) => c.status === "finished").length;
			const in_progress = calls.filter(
				(c) => c.status === "in_progress"
			).length;
			const failed = calls.filter((c) => c.status === "failed").length;

			// Calculate sentiment stats
			const positive = calls.filter((c) => c.sentiment === "positive").length;
			const neutral = calls.filter((c) => c.sentiment === "neutral").length;
			const negative = calls.filter((c) => c.sentiment === "negative").length;

			// Calculate direction stats
			const inbound = calls.filter((c) => c.direction === "inbound").length;
			const outbound = calls.filter((c) => c.direction === "outbound").length;

			setStats({
				scheduled,
				finished,
				in_progress,
				failed,
				total,
				sentimentStats: { positive, neutral, negative },
				directionStats: { inbound, outbound },
			});
		} catch (error) {
			console.error("Error fetching call stats:", error);
		} finally {
			setLoading(false);
		}
	};

	const sentimentData = [
		{
			name: "Positive",
			value: stats.sentimentStats.positive,
			count: stats.sentimentStats.positive,
		},
		{
			name: "Neutral",
			value: stats.sentimentStats.neutral,
			count: stats.sentimentStats.neutral,
		},
		{
			name: "Negative",
			value: stats.sentimentStats.negative,
			count: stats.sentimentStats.negative,
		},
	].filter((item) => item.value > 0);

	const directionData = [
		{
			name: "Inbound",
			value: stats.directionStats.inbound,
			count: stats.directionStats.inbound,
		},
		{
			name: "Outbound",
			value: stats.directionStats.outbound,
			count: stats.directionStats.outbound,
		},
	].filter((item) => item.value > 0);

	if (loading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-lg">Loading...</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Calls</h1>
			</div>

			{/* Status Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				<Card className="bg-blue-50 border-blue-200">
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-blue-800">
							Scheduled
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-3xl font-bold text-blue-900">
							{stats.scheduled}
						</div>
					</CardContent>
				</Card>

				<Card className="bg-green-50 border-green-200">
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-green-800">
							Finished
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-3xl font-bold text-green-900">
							{stats.finished}
						</div>
					</CardContent>
				</Card>

				<Card className="bg-yellow-50 border-yellow-200">
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-yellow-800">
							In progress
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-3xl font-bold text-yellow-900">
							{stats.in_progress}
						</div>
					</CardContent>
				</Card>

				<Card className="bg-red-50 border-red-200">
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-red-800">
							Failed
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-3xl font-bold text-red-900">
							{stats.failed}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Charts */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Sentiment Chart */}
				<Card>
					<CardHeader>
						<CardTitle>Sentiment Analysis</CardTitle>
						<CardDescription>Call sentiment distribution</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="h-64">
							<ResponsiveContainer width="100%" height="100%">
								<PieChart>
									<Pie
										data={sentimentData}
										cx="50%"
										cy="50%"
										innerRadius={60}
										outerRadius={100}
										paddingAngle={5}
										dataKey="value"
									>
										{sentimentData.map((entry, index) => (
											<Cell
												key={`cell-${index}`}
												fill={
													SENTIMENT_COLORS[
														entry.name.toLowerCase() as keyof typeof SENTIMENT_COLORS
													]
												}
											/>
										))}
									</Pie>
									<Tooltip content={<CustomTooltip />} />
									<Legend />
								</PieChart>
							</ResponsiveContainer>
						</div>
						<div className="mt-4 space-y-2">
							{sentimentData.map((item) => (
								<div
									key={item.name}
									className="flex items-center justify-between text-sm"
								>
									<div className="flex items-center space-x-2">
										<div
											className="w-3 h-3 rounded-full"
											style={{
												backgroundColor:
													SENTIMENT_COLORS[
														item.name.toLowerCase() as keyof typeof SENTIMENT_COLORS
													],
											}}
										/>
										<span>{item.name}</span>
									</div>
									<span>({item.count})</span>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Direction Chart */}
				<Card>
					<CardHeader>
						<CardTitle>Call Direction</CardTitle>
						<CardDescription>Inbound vs outbound calls</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="h-64">
							<ResponsiveContainer width="100%" height="100%">
								<PieChart>
									<Pie
										data={directionData}
										cx="50%"
										cy="50%"
										innerRadius={60}
										outerRadius={100}
										paddingAngle={5}
										dataKey="value"
									>
										{directionData.map((entry, index) => (
											<Cell
												key={`cell-${index}`}
												fill={
													DIRECTION_COLORS[
														entry.name.toLowerCase() as keyof typeof DIRECTION_COLORS
													]
												}
											/>
										))}
									</Pie>
									<Tooltip content={<CustomTooltip />} />
									<Legend />
								</PieChart>
							</ResponsiveContainer>
						</div>
						<div className="mt-4 space-y-2">
							{directionData.map((item) => (
								<div
									key={item.name}
									className="flex items-center justify-between text-sm"
								>
									<div className="flex items-center space-x-2">
										<div
											className="w-3 h-3 rounded-full"
											style={{
												backgroundColor:
													DIRECTION_COLORS[
														item.name.toLowerCase() as keyof typeof DIRECTION_COLORS
													],
											}}
										/>
										<span>{item.name}</span>
									</div>
									<span>({item.count})</span>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
