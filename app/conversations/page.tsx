"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ProtectedRoute } from "@/components/layout/protected-route"
import { Navbar } from "@/components/layout/navbar"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/hooks/use-auth"
import { MessageSquare, Search, Filter } from "lucide-react"
import Link from "next/link"
import { PageTransition, StaggerContainer, StaggerItem } from "@/components/ui/page-transition"
import { CardSkeleton } from "@/components/ui/skeleton"

type Conversation = {
  id: string
  title: string
  description: string | null
  status: "open" | "closed" | "pending"
  priority: "low" | "medium" | "high"
  created_at: string
}

export default function ConversationsPage() {
  const { profile } = useAuth()
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [filteredConversations, setFilteredConversations] = useState<Conversation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  useEffect(() => {
    if (profile) {
      fetchConversations()
    }
  }, [profile])

  useEffect(() => {
    filterConversations()
  }, [conversations, searchTerm, statusFilter])

  const fetchConversations = async () => {
    try {
      if (profile?.role === "administrator") {
        // Admin can see all conversations
        const { data, error } = await supabase
          .from("conversations")
          .select("*")
          .order("created_at", { ascending: false })

        if (error) throw error
        setConversations(data || [])
      } else {
        // Client can only see their own conversations
        const { data: clientData } = await supabase.from("clients").select("id").eq("user_id", profile?.id).single()

        if (clientData) {
          const { data, error } = await supabase
            .from("conversations")
            .select("*")
            .eq("client_id", clientData.id)
            .order("created_at", { ascending: false })

          if (error) throw error
          setConversations(data || [])
        }
      }
    } catch (error) {
      console.error("Error fetching conversations:", error)
    } finally {
      setLoading(false)
    }
  }

  const filterConversations = () => {
    let filtered = conversations

    if (searchTerm) {
      filtered = filtered.filter(
        (conv) =>
          conv.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          conv.description?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((conv) => conv.status === statusFilter)
    }

    setFilteredConversations(filtered)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-blue-100 text-blue-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <PageTransition>
          <div className="min-h-screen bg-gray-50">
            <Navbar />
            <div className="flex items-center justify-center min-h-screen">
              <div className="space-y-6 w-full max-w-4xl mx-auto px-4">
                <CardSkeleton />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                </div>
              </div>
            </div>
          </div>
        </PageTransition>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <PageTransition>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Conversations</h1>
              <p className="text-gray-600">
                {profile?.role === "administrator"
                  ? "Manage all client conversations"
                  : "View and manage your conversations"}
              </p>
            </div>

            {/* Search and Filter */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search conversations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                      <option value="all">All Status</option>
                      <option value="open">Open</option>
                      <option value="pending">Pending</option>
                      <option value="closed">Closed</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Conversations List */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {filteredConversations.length} Conversation{filteredConversations.length !== 1 ? "s" : ""}
                </CardTitle>
                <CardDescription>
                  {searchTerm || statusFilter !== "all" ? "Filtered results" : "All your conversations"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredConversations.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">
                      {searchTerm || statusFilter !== "all"
                        ? "No conversations match your filters"
                        : "No conversations yet"}
                    </p>
                    {!searchTerm && statusFilter === "all" && (
                      <p className="text-sm text-gray-400">Your conversations will appear here</p>
                    )}
                  </div>
                ) : (
                  <StaggerContainer className="space-y-4">
                    {filteredConversations.map((conversation) => (
                      <StaggerItem key={conversation.id}>
                        <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="flex-1">
                            <h3 className="font-medium text-lg">{conversation.title}</h3>
                            <p className="text-gray-600 mt-1">{conversation.description}</p>
                            <p className="text-sm text-gray-400 mt-2">
                              Created: {new Date(conversation.created_at).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge className={getPriorityColor(conversation.priority)}>{conversation.priority}</Badge>
                            <Badge className={getStatusColor(conversation.status)}>{conversation.status}</Badge>
                            <Button asChild variant="outline">
                              <Link href={`/conversations/${conversation.id}`}>View Details</Link>
                            </Button>
                          </div>
                        </div>
                      </StaggerItem>
                    ))}
                  </StaggerContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
      </PageTransition>
    </ProtectedRoute>
  )
}
