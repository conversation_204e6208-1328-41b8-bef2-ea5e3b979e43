"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ProtectedRoute } from "@/components/layout/protected-route"
import { Navbar } from "@/components/layout/navbar"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/hooks/use-auth"
import { ArrowLeft, Calendar, User, Building } from "lucide-react"
import Link from "next/link"

type ConversationDetails = {
  id: string
  title: string
  description: string | null
  status: "open" | "closed" | "pending"
  priority: "low" | "medium" | "high"
  created_at: string
  updated_at: string
  client: {
    id: string
    company_name: string | null
    user: {
      full_name: string | null
      email: string
    }
  }
}

export default function ConversationDetailPage() {
  const params = useParams()
  const { profile } = useAuth()
  const [conversation, setConversation] = useState<ConversationDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    if (params.id && profile) {
      fetchConversationDetails()
    }
  }, [params.id, profile])

  const fetchConversationDetails = async () => {
    try {
      const { data, error } = await supabase
        .from("conversations")
        .select(`
          *,
          clients!inner(
            id,
            company_name,
            profiles!inner(
              full_name,
              email
            )
          )
        `)
        .eq("id", params.id)
        .single()

      if (error) throw error

      // Check if user has permission to view this conversation
      if (profile?.role === "client") {
        const { data: clientData } = await supabase.from("clients").select("id").eq("user_id", profile.id).single()

        if (clientData?.id !== (data.clients as any).id) {
          setError("You do not have permission to view this conversation")
          return
        }
      }

      const formattedData = {
        ...data,
        client: {
          id: (data.clients as any).id,
          company_name: (data.clients as any).company_name,
          user: {
            full_name: (data.clients as any).profiles.full_name,
            email: (data.clients as any).profiles.email,
          },
        },
      }

      setConversation(formattedData)
    } catch (error) {
      console.error("Error fetching conversation details:", error)
      setError("Failed to load conversation details")
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-blue-100 text-blue-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-lg">Loading...</div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
              <Button asChild className="mt-4">
                <Link href="/conversations">Back to Conversations</Link>
              </Button>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    )
  }

  if (!conversation) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="text-center py-8">
              <p className="text-gray-600">Conversation not found</p>
              <Button asChild className="mt-4">
                <Link href="/conversations">Back to Conversations</Link>
              </Button>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            {/* Back Button */}
            <Button asChild variant="ghost" className="mb-4">
              <Link href="/conversations">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Conversations
              </Link>
            </Button>

            {/* Conversation Header */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl">{conversation.title}</CardTitle>
                    <CardDescription className="mt-2">{conversation.description}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getPriorityColor(conversation.priority)}>{conversation.priority} priority</Badge>
                    <Badge className={getStatusColor(conversation.status)}>{conversation.status}</Badge>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Conversation Details */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Conversation Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900">Description</h3>
                      <p className="mt-1 text-gray-600">{conversation.description || "No description provided"}</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="font-medium text-gray-900">Status</h3>
                        <Badge className={`mt-1 ${getStatusColor(conversation.status)}`}>{conversation.status}</Badge>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">Priority</h3>
                        <Badge className={`mt-1 ${getPriorityColor(conversation.priority)}`}>
                          {conversation.priority}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="font-medium text-gray-900 flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          Created
                        </h3>
                        <p className="mt-1 text-gray-600">
                          {new Date(conversation.created_at).toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          Last Updated
                        </h3>
                        <p className="mt-1 text-gray-600">
                          {new Date(conversation.updated_at).toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Client Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 flex items-center">
                        <User className="h-4 w-4 mr-2" />
                        Contact Person
                      </h3>
                      <p className="mt-1 text-gray-600">{conversation.client.user.full_name || "Not specified"}</p>
                      <p className="text-sm text-gray-500">{conversation.client.user.email}</p>
                    </div>

                    {conversation.client.company_name && (
                      <div>
                        <h3 className="font-medium text-gray-900 flex items-center">
                          <Building className="h-4 w-4 mr-2" />
                          Company
                        </h3>
                        <p className="mt-1 text-gray-600">{conversation.client.company_name}</p>
                      </div>
                    )}

                    {profile?.role === "administrator" && (
                      <div className="pt-4 border-t">
                        <Button asChild variant="outline" className="w-full bg-transparent">
                          <Link href={`/admin/clients/${conversation.client.id}`}>View Client Profile</Link>
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
