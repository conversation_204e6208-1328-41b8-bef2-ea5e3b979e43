import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { AuthProvider } from "@/hooks/use-auth"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Client Portal - Authentication System",
  description: "Role-based authentication system with Supabase",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <div id="page-transition-container">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  )
}
