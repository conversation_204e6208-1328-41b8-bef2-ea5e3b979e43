"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ProtectedRoute } from "@/components/layout/protected-route"
import { Navbar } from "@/components/layout/navbar"
import { supabase } from "@/lib/supabase"
import { MessageSquare, Search, Filter, Users } from "lucide-react"
import Link from "next/link"

type AdminConversation = {
  id: string
  title: string
  description: string | null
  status: "open" | "closed" | "pending"
  priority: "low" | "medium" | "high"
  created_at: string
  client: {
    id: string
    company_name: string | null
    user: {
      full_name: string | null
      email: string
    }
  }
}

export default function AdminConversationsPage() {
  const [conversations, setConversations] = useState<AdminConversation[]>([])
  const [filteredConversations, setFilteredConversations] = useState<AdminConversation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")

  useEffect(() => {
    fetchConversations()
  }, [])

  useEffect(() => {
    filterConversations()
  }, [conversations, searchTerm, statusFilter, priorityFilter])

  const fetchConversations = async () => {
    try {
      const { data, error } = await supabase
        .from("conversations")
        .select(`
          *,
          clients!inner(
            id,
            company_name,
            profiles!inner(
              full_name,
              email
            )
          )
        `)
        .order("created_at", { ascending: false })

      if (error) throw error

      const formattedData =
        data?.map((conv) => ({
          id: conv.id,
          title: conv.title,
          description: conv.description,
          status: conv.status as "open" | "closed" | "pending",
          priority: conv.priority as "low" | "medium" | "high",
          created_at: conv.created_at,
          client: {
            id: (conv.clients as any).id,
            company_name: (conv.clients as any).company_name,
            user: {
              full_name: (conv.clients as any).profiles.full_name,
              email: (conv.clients as any).profiles.email,
            },
          },
        })) || []

      setConversations(formattedData)
    } catch (error) {
      console.error("Error fetching conversations:", error)
    } finally {
      setLoading(false)
    }
  }

  const filterConversations = () => {
    let filtered = conversations

    if (searchTerm) {
      filtered = filtered.filter(
        (conv) =>
          conv.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          conv.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          conv.client.user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          conv.client.company_name?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((conv) => conv.status === statusFilter)
    }

    if (priorityFilter !== "all") {
      filtered = filtered.filter((conv) => conv.priority === priorityFilter)
    }

    setFilteredConversations(filtered)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-blue-100 text-blue-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (loading) {
    return (
      <ProtectedRoute requiredRole="administrator">
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-lg">Loading...</div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRole="administrator">
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">All Conversations</h1>
              <p className="text-gray-600">Manage conversations from all clients</p>
            </div>

            {/* Search and Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search conversations, clients..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4 text-gray-400" />
                      <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                      >
                        <option value="all">All Status</option>
                        <option value="open">Open</option>
                        <option value="pending">Pending</option>
                        <option value="closed">Closed</option>
                      </select>
                    </div>
                    <select
                      value={priorityFilter}
                      onChange={(e) => setPriorityFilter(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                      <option value="all">All Priority</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Conversations List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  {filteredConversations.length} Conversation{filteredConversations.length !== 1 ? "s" : ""}
                </CardTitle>
                <CardDescription>
                  {searchTerm || statusFilter !== "all" || priorityFilter !== "all"
                    ? "Filtered results"
                    : "All conversations from all clients"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredConversations.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">
                      {searchTerm || statusFilter !== "all" || priorityFilter !== "all"
                        ? "No conversations match your filters"
                        : "No conversations yet"}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredConversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-medium text-lg">{conversation.title}</h3>
                              <p className="text-gray-600 mt-1">{conversation.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center mt-3 space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-1" />
                              <span>
                                {conversation.client.user.full_name || "No name"}
                                {conversation.client.company_name && ` (${conversation.client.company_name})`}
                              </span>
                            </div>
                            <span>•</span>
                            <span>{new Date(conversation.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3 ml-4">
                          <Badge className={getPriorityColor(conversation.priority)}>{conversation.priority}</Badge>
                          <Badge className={getStatusColor(conversation.status)}>{conversation.status}</Badge>
                          <Button asChild variant="outline" size="sm">
                            <Link href={`/conversations/${conversation.id}`}>View Details</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
