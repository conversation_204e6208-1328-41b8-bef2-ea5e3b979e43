"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ProtectedRoute } from "@/components/layout/protected-route"
import { Navbar } from "@/components/layout/navbar"
import { supabase } from "@/lib/supabase"
import { Users, Search, Building, MessageSquare } from "lucide-react"
import Link from "next/link"

type ClientData = {
  id: string
  company_name: string | null
  status: "active" | "inactive" | "pending"
  created_at: string
  user: {
    full_name: string | null
    email: string
  }
  conversation_count: number
}

export default function AdminClientsPage() {
  const [clients, setClients] = useState<ClientData[]>([])
  const [filteredClients, setFilteredClients] = useState<ClientData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    fetchClients()
  }, [])

  useEffect(() => {
    filterClients()
  }, [clients, searchTerm])

  const fetchClients = async () => {
    try {
      const { data, error } = await supabase
        .from("clients")
        .select(`
          *,
          profiles!inner(
            full_name,
            email
          ),
          conversations(count)
        `)
        .order("created_at", { ascending: false })

      if (error) throw error

      const formattedData =
        data?.map((client) => ({
          id: client.id,
          company_name: client.company_name,
          status: client.status as "active" | "inactive" | "pending",
          created_at: client.created_at,
          user: {
            full_name: (client.profiles as any).full_name,
            email: (client.profiles as any).email,
          },
          conversation_count: (client.conversations as any)?.length || 0,
        })) || []

      setClients(formattedData)
    } catch (error) {
      console.error("Error fetching clients:", error)
    } finally {
      setLoading(false)
    }
  }

  const filterClients = () => {
    let filtered = clients

    if (searchTerm) {
      filtered = filtered.filter(
        (client) =>
          client.user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          client.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          client.company_name?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    setFilteredClients(filtered)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "inactive":
        return "bg-red-100 text-red-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (loading) {
    return (
      <ProtectedRoute requiredRole="administrator">
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-lg">Loading...</div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRole="administrator">
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Client Management</h1>
              <p className="text-gray-600">Manage all client accounts and their information</p>
            </div>

            {/* Search */}
            <Card>
              <CardContent className="pt-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search clients by name, email, or company..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Clients List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  {filteredClients.length} Client{filteredClients.length !== 1 ? "s" : ""}
                </CardTitle>
                <CardDescription>{searchTerm ? "Filtered results" : "All registered clients"}</CardDescription>
              </CardHeader>
              <CardContent>
                {filteredClients.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">
                      {searchTerm ? "No clients match your search" : "No clients registered yet"}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredClients.map((client) => (
                      <div
                        key={client.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <Users className="h-5 w-5 text-blue-600" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-lg">{client.user.full_name || "No name provided"}</h3>
                            <p className="text-gray-600">{client.user.email}</p>
                            {client.company_name && (
                              <p className="text-sm text-gray-500 flex items-center mt-1">
                                <Building className="h-3 w-3 mr-1" />
                                {client.company_name}
                              </p>
                            )}
                            <p className="text-xs text-gray-400 mt-1">
                              Joined: {new Date(client.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="text-center">
                            <div className="flex items-center text-sm text-gray-600">
                              <MessageSquare className="h-4 w-4 mr-1" />
                              {client.conversation_count}
                            </div>
                            <p className="text-xs text-gray-400">conversations</p>
                          </div>
                          <Badge className={getStatusColor(client.status)}>{client.status}</Badge>
                          <Button asChild variant="outline" size="sm">
                            <Link href={`/admin/clients/${client.id}`}>View Details</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
