import { ProtectedRoute } from "@/components/layout/protected-route";
import { UmbrellaSidebar } from "@/components/layout/umbrella-sidebar";
import React from "react";

export default function ProtectedLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<ProtectedRoute>
			<div className="flex h-screen bg-gray-50">
				<UmbrellaSidebar />
				{children}
			</div>
		</ProtectedRoute>
	);
}
