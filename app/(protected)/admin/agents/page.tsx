"use client"

import { ProtectedRoute } from "@/components/layout/protected-route"
import { UmbrellaSidebar } from "@/components/layout/umbrella-sidebar"
import { AgentsGrid } from "@/components/agents/agents-grid"
import { PageTransition } from "@/components/ui/page-transition"

export default function AgentsPage() {
  return (
    <ProtectedRoute requiredRole="administrator">
      <PageTransition>
        <div className="flex h-screen bg-gray-50">
          <UmbrellaSidebar />
          <main className="flex-1 overflow-y-auto">
            <div className="p-8">
              <AgentsGrid />
            </div>
          </main>
        </div>
      </PageTransition>
    </ProtectedRoute>
  )
}
